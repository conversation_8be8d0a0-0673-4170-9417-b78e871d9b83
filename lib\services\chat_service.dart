import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:uuid/uuid.dart';
import '../models/chat_model.dart';
import '../models/user_model.dart';

class ChatService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _chatsCollection = 'chats';
  static const String _messagesCollection = 'messages';
  static const Uuid _uuid = Uuid();

  /// Create or get existing chat between two users
  static Future<ChatModel?> createOrGetChat({
    required String currentUserId,
    required String otherUserId,
    required String currentUserName,
    required String otherUserName,
  }) async {
    try {
      // Check if chat already exists
      final existingChat = await _findExistingChat(currentUserId, otherUserId);
      if (existingChat != null) {
        return existingChat;
      }

      // Create new chat
      final chatId = _uuid.v4();
      final now = DateTime.now();

      final chat = ChatModel(
        id: chatId,
        participants: [currentUserId, otherUserId],
        lastMessage: '',
        lastMessageSenderId: '',
        lastMessageTime: now,
        unreadCounts: {
          currentUserId: 0,
          otherUserId: 0,
        },
        createdAt: now,
        updatedAt: now,
      );

      await _firestore
          .collection(_chatsCollection)
          .doc(chatId)
          .set(chat.toMap());

      return chat;
    } catch (e) {
      print('Error creating/getting chat: $e');
      return null;
    }
  }

  /// Find existing chat between two users
  static Future<ChatModel?> _findExistingChat(String userId1, String userId2) async {
    try {
      final querySnapshot = await _firestore
          .collection(_chatsCollection)
          .where('participants', arrayContains: userId1)
          .get();

      for (final doc in querySnapshot.docs) {
        final chat = ChatModel.fromMap(doc.data());
        if (chat.participants.contains(userId2)) {
          return chat;
        }
      }

      return null;
    } catch (e) {
      print('Error finding existing chat: $e');
      return null;
    }
  }

  /// Get user's chats stream
  static Stream<List<ChatModel>> getUserChatsStream(String userId) {
    print('Getting chats for user: $userId');
    return _firestore
        .collection(_chatsCollection)
        .where('participants', arrayContains: userId)
        .snapshots()
        .map((snapshot) {
          print('Chat snapshot received: ${snapshot.docs.length} documents');
          final chats = snapshot.docs
              .map((doc) {
                try {
                  return ChatModel.fromMap(doc.data());
                } catch (e) {
                  print('Error parsing chat document ${doc.id}: $e');
                  return null;
                }
              })
              .where((chat) => chat != null && chat.isActive)
              .cast<ChatModel>()
              .toList();

          // Sort by lastMessageTime
          chats.sort((a, b) => b.lastMessageTime.compareTo(a.lastMessageTime));
          print('Returning ${chats.length} active chats');
          return chats;
        });
  }

  /// Send a message
  static Future<MessageModel?> sendMessage({
    required String chatId,
    required String senderId,
    required String senderName,
    required String content,
    MessageType type = MessageType.text,
  }) async {
    try {
      final messageId = _uuid.v4();
      final now = DateTime.now();

      final message = MessageModel(
        id: messageId,
        chatId: chatId,
        senderId: senderId,
        senderName: senderName,
        content: content,
        type: type,
        createdAt: now,
        readBy: [senderId], // Sender has read the message
      );

      // Save message
      await _firestore
          .collection(_messagesCollection)
          .doc(messageId)
          .set(message.toMap());

      // Update chat's last message
      await _updateChatLastMessage(chatId, content, senderId, now);

      return message;
    } catch (e) {
      print('Error sending message: $e');
      return null;
    }
  }

  /// Update chat's last message
  static Future<void> _updateChatLastMessage(
    String chatId,
    String lastMessage,
    String senderId,
    DateTime timestamp,
  ) async {
    try {
      // Get chat to update unread counts
      final chatDoc = await _firestore
          .collection(_chatsCollection)
          .doc(chatId)
          .get();

      if (chatDoc.exists) {
        final chat = ChatModel.fromMap(chatDoc.data()!);
        final updatedUnreadCounts = Map<String, int>.from(chat.unreadCounts);

        // Increment unread count for other participants
        for (final participantId in chat.participants) {
          if (participantId != senderId) {
            updatedUnreadCounts[participantId] =
                (updatedUnreadCounts[participantId] ?? 0) + 1;
          }
        }

        await _firestore
            .collection(_chatsCollection)
            .doc(chatId)
            .update({
          'lastMessage': lastMessage,
          'lastMessageSenderId': senderId,
          'lastMessageTime': Timestamp.fromDate(timestamp),
          'unreadCounts': updatedUnreadCounts,
          'updatedAt': Timestamp.fromDate(timestamp),
        });
      }
    } catch (e) {
      print('Error updating chat last message: $e');
    }
  }

  /// Get messages stream for a chat
  static Stream<List<MessageModel>> getMessagesStream(String chatId) {
    print('Getting messages for chat: $chatId');
    return _firestore
        .collection(_messagesCollection)
        .where('chatId', isEqualTo: chatId)
        .snapshots()
        .map((snapshot) {
          print('Messages snapshot received: ${snapshot.docs.length} documents');
          final messages = snapshot.docs
              .map((doc) {
                try {
                  return MessageModel.fromMap(doc.data());
                } catch (e) {
                  print('Error parsing message document ${doc.id}: $e');
                  return null;
                }
              })
              .where((message) => message != null && !message.isDeleted)
              .cast<MessageModel>()
              .toList();

          // Sort by createdAt descending (newest first)
          messages.sort((a, b) => b.createdAt.compareTo(a.createdAt));
          print('Returning ${messages.length} active messages');
          return messages;
        });
  }

  /// Mark messages as read
  static Future<void> markMessagesAsRead({
    required String chatId,
    required String userId,
  }) async {
    try {
      // Get all messages for this chat (we'll filter in memory to avoid index issues)
      final messagesSnapshot = await _firestore
          .collection(_messagesCollection)
          .where('chatId', isEqualTo: chatId)
          .get();

      final batch = _firestore.batch();

      for (final doc in messagesSnapshot.docs) {
        final message = MessageModel.fromMap(doc.data());
        // Only update messages not sent by current user and not already read
        if (message.senderId != userId && !message.isReadBy(userId)) {
          final updatedReadBy = List<String>.from(message.readBy)..add(userId);
          batch.update(doc.reference, {'readBy': updatedReadBy});
        }
      }

      await batch.commit();

      // Reset unread count for this user in chat
      await _firestore
          .collection(_chatsCollection)
          .doc(chatId)
          .update({
        'unreadCounts.$userId': 0,
      });
    } catch (e) {
      print('Error marking messages as read: $e');
    }
  }

  /// Delete a message
  static Future<bool> deleteMessage(String messageId) async {
    try {
      await _firestore
          .collection(_messagesCollection)
          .doc(messageId)
          .update({'isDeleted': true});
      return true;
    } catch (e) {
      print('Error deleting message: $e');
      return false;
    }
  }

  /// Get chat by ID
  static Future<ChatModel?> getChatById(String chatId) async {
    try {
      final doc = await _firestore
          .collection(_chatsCollection)
          .doc(chatId)
          .get();

      if (doc.exists) {
        return ChatModel.fromMap(doc.data()!);
      }
      return null;
    } catch (e) {
      print('Error getting chat by ID: $e');
      return null;
    }
  }

  /// Get other participant info from chat
  static String getOtherParticipantId(ChatModel chat, String currentUserId) {
    return chat.participants.firstWhere(
      (id) => id != currentUserId,
      orElse: () => '',
    );
  }

  /// Get unread count for user
  static int getUnreadCount(ChatModel chat, String userId) {
    return chat.unreadCounts[userId] ?? 0;
  }

  /// Create a test chat for debugging
  static Future<void> createTestChat(String userId) async {
    try {
      final chatId = _uuid.v4();
      final now = DateTime.now();

      final testChat = ChatModel(
        id: chatId,
        participants: [userId, 'test_user_id'],
        lastMessage: 'Hello! This is a test message.',
        lastMessageSenderId: 'test_user_id',
        lastMessageTime: now,
        unreadCounts: {
          userId: 1,
          'test_user_id': 0,
        },
        createdAt: now,
        updatedAt: now,
      );

      await _firestore
          .collection(_chatsCollection)
          .doc(chatId)
          .set(testChat.toMap());

      print('Test chat created with ID: $chatId');
    } catch (e) {
      print('Error creating test chat: $e');
    }
  }
}
